# Enhanced Stream ASR 开发者手册

## 📖 目录

1. [项目概述](#项目概述)
2. [架构设计](#架构设计)
3. [开发环境搭建](#开发环境搭建)
4. [代码结构](#代码结构)
5. [核心模块详解](#核心模块详解)
6. [API设计](#api设计)
7. [数据流程](#数据流程)
8. [扩展开发](#扩展开发)
9. [测试指南](#测试指南)
10. [部署指南](#部署指南)
11. [性能优化](#性能优化)
12. [故障排查](#故障排查)

---

## 🎯 项目概述

Enhanced Stream ASR 是一个企业级的实时语音识别系统，采用现代化的微服务架构设计，支持高并发、低延迟的语音识别服务。

### 技术栈

- **后端框架**: FastAPI + Uvicorn
- **WebSocket**: FastAPI WebSocket
- **深度学习**: PyTorch + ONNX Runtime
- **音频处理**: WebRTC VAD + NumPy
- **配置管理**: YAML + Pydantic
- **监控告警**: 自研监控系统
- **前端**: 原生JavaScript + Chart.js

### 设计原则

1. **高性能**: 优化的推理引擎和内存管理
2. **高可用**: 完善的错误处理和恢复机制
3. **可扩展**: 模块化设计，易于扩展新功能
4. **可维护**: 清晰的代码结构和完善的文档
5. **可监控**: 全面的性能监控和告警系统

---

## 🏗️ 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                        Client Layer                         │
├─────────────────────────────────────────────────────────────┤
│  Web UI  │  Mobile App  │  Third-party Integration         │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      API Gateway                            │
├─────────────────────────────────────────────────────────────┤
│  HTTP API  │  WebSocket API  │  Monitoring API              │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Business Logic                           │
├─────────────────────────────────────────────────────────────┤
│  Session Manager  │  Protocol Handler  │  Error Handler    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Core Engines                            │
├─────────────────────────────────────────────────────────────┤
│  VAD Processor  │  LID Engine  │  ASR Engine               │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Infrastructure                            │
├─────────────────────────────────────────────────────────────┤
│  ONNX Runtime  │  Config Manager  │  Monitoring System     │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. 会话管理器 (Session Manager)
- 管理WebSocket连接和会话生命周期
- 处理并发连接和资源分配
- 实现会话状态机和超时管理

#### 2. 协议处理器 (Protocol Handler)
- 处理WebSocket消息协议
- 实现三阶段通信协议（握手、数据传输、断开）
- 消息序列化和反序列化

#### 3. 音频处理引擎
- **VAD处理器**: 语音活动检测和音频分段
- **LID引擎**: 语种识别和渐进式检测
- **ASR引擎**: 流式语音识别和文本后处理

#### 4. ONNX引擎池
- 模型加载和推理管理
- 动态扩缩容和负载均衡
- 内存优化和资源回收

---

## 🛠️ 开发环境搭建

### 1. 环境要求

```bash
# Python版本
python --version  # >= 3.8

# 系统依赖 (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    python3-dev \
    libffi-dev \
    libssl-dev \
    libasound2-dev \
    portaudio19-dev

# 系统依赖 (CentOS/RHEL)
sudo yum groupinstall -y "Development Tools"
sudo yum install -y \
    python3-devel \
    libffi-devel \
    openssl-devel \
    alsa-lib-devel \
    portaudio-devel
```

### 2. 项目设置

```bash
# 克隆项目
git clone <repository-url>
cd enhanced_stream_asr

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -r requirements-dev.txt

# 安装pre-commit钩子
pre-commit install

# 运行测试确保环境正常
python -m pytest tests/
```

### 3. IDE配置

#### VS Code配置 (`.vscode/settings.json`)

```json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.sortImports.args": ["--profile", "black"],
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": true
    }
}
```

#### PyCharm配置
1. 设置Python解释器为虚拟环境
2. 启用代码格式化工具（Black）
3. 配置代码检查工具（Pylint, Flake8）
4. 设置测试运行器为pytest

---

## 📁 代码结构

```
enhanced_stream_asr/
├── api/                    # API接口层
│   ├── http/              # HTTP API
│   ├── websocket/         # WebSocket API
│   └── monitoring/        # 监控API
├── core/                  # 核心业务逻辑
│   ├── session/           # 会话管理
│   ├── audio/             # 音频处理
│   ├── lid/               # 语种识别
│   ├── asr/               # 语音识别
│   └── engines/           # 推理引擎
├── utils/                 # 工具模块
│   ├── config/            # 配置管理
│   ├── monitoring/        # 监控系统
│   ├── exceptions.py      # 异常处理
│   └── logger.py          # 日志系统
├── web/                   # Web前端
│   └── static/            # 静态文件
├── configs/               # 配置文件
│   ├── server_config.yaml
│   ├── lid_config.yaml
│   └── lang_configs/      # 语种配置
├── models/                # 模型文件
├── tests/                 # 测试代码
├── scripts/               # 工具脚本
├── docs/                  # 文档
└── server.py              # 主服务器
```

### 模块职责

| 模块 | 职责 | 主要类/函数 |
|------|------|-------------|
| `api.websocket` | WebSocket协议处理 | `WebSocketHandler`, `WebSocketProtocol` |
| `core.session` | 会话生命周期管理 | `SessionManager`, `Session` |
| `core.audio` | 音频处理和VAD | `VADProcessor`, `FeatureExtractor` |
| `core.lid` | 语种识别 | `LIDEngine`, `ProgressiveLID` |
| `core.asr` | 语音识别 | `StreamingASREngine`, `TextProcessor` |
| `core.engines` | ONNX推理引擎 | `PooledONNXEngine`, `SessionPool` |
| `utils.config` | 配置管理 | `ConfigManager`, `ConfigValidator` |
| `utils.monitoring` | 监控和告警 | `PerformanceMonitor`, `AlertManager` |

---

## 🔧 核心模块详解

### 1. 会话管理器 (SessionManager)

#### 核心功能
- WebSocket连接管理
- 会话状态跟踪
- 资源分配和回收
- 并发控制

#### 关键方法

```python
class SessionManager:
    async def create_session(self, session_id: str, client_id: str, 
                           handshake_req: HandshakeRequest, 
                           websocket: WebSocket) -> bool:
        """创建新会话"""
        
    async def process_audio_data(self, session_id: str, 
                               audio_message: AudioDataMessage) -> Dict[str, Any]:
        """处理音频数据"""
        
    async def cleanup_session(self, session_id: str):
        """清理会话资源"""
        
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
```

#### 状态机设计

```python
class SessionState(Enum):
    INITIALIZING = "initializing"      # 初始化中
    WAITING_FOR_SPEECH = "waiting"     # 等待语音
    DETECTING_LANGUAGE = "detecting"   # 检测语种
    PROCESSING_SPEECH = "processing"   # 处理语音
    ERROR = "error"                    # 错误状态
    TERMINATED = "terminated"          # 已终止
```

### 2. 流式ASR引擎 (StreamingASREngine)

#### 核心算法
- 编码器-CTC架构
- 流式特征提取
- 实时解码和后处理

#### 关键方法

```python
class StreamingASREngine:
    async def process_audio_chunk(self, audio_data: Union[bytes, np.ndarray], 
                                 is_final: bool = False) -> Dict[str, Any]:
        """处理音频块"""
        
    async def _run_encoder(self, chunk: torch.Tensor) -> Optional[torch.Tensor]:
        """运行编码器推理"""
        
    def _ctc_prefix_beam_search(self, ctc_probs: torch.Tensor, 
                               beam_size: int = 10) -> Tuple[List[int], List[float]]:
        """CTC前缀束搜索解码"""
```

#### 性能优化
- 滑动窗口缓存机制
- 批处理推理
- 内存池管理

### 3. ONNX引擎池 (PooledONNXEngine)

#### 设计目标
- 支持高并发推理
- 动态扩缩容
- 内存优化

#### 实现原理

```python
class PooledONNXEngine:
    def __init__(self, model_path: str, config: Dict[str, Any]):
        self.session_pool = SessionPool(
            model_path=model_path,
            initial_size=config.get('initial_pool_size', 2),
            max_size=config.get('max_pool_size', 10),
            providers=config.get('providers', ['CPUExecutionProvider'])
        )
    
    async def infer(self, inputs: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """执行推理"""
        async with self.session_pool.get_session() as session:
            return await self._run_inference(session, inputs)
```

---

## 🌐 API设计

### WebSocket协议

#### 消息格式

```typescript
// 基础消息结构
interface BaseMessage {
    type: string;
    timestamp: number;
    sequence_id?: number;
}

// 握手请求
interface HandshakeRequest extends BaseMessage {
    type: 'handshake';
    client_id: string;
    language: string;
    auto_language_detection: boolean;
    audio_config: {
        sample_rate: number;
        channels: number;
        chunk_duration: number;
    };
}

// 音频数据
interface AudioDataMessage extends BaseMessage {
    type: 'audio_data';
    audio_data: number[];  // Int16Array
    is_last: boolean;
}

// 识别结果
interface RecognitionResult extends BaseMessage {
    type: 'recognition_result';
    text: string;
    is_final: boolean;
    confidence: number;
    language: string;
    processing_time: number;
}
```

#### 状态转换

```
[连接建立] → [握手] → [数据传输] → [断开连接]
     ↓           ↓          ↓           ↓
  CONNECTING → HANDSHAKE → STREAMING → DISCONNECTED
                  ↓          ↓
               [错误] ← [处理中] → [结果]
```

### HTTP API

#### RESTful设计

```python
# 路由定义
@app.get("/api/languages")
async def get_supported_languages() -> Dict[str, Any]:
    """获取支持的语种列表"""

@app.get("/api/status")
async def get_server_status() -> Dict[str, Any]:
    """获取服务器状态"""

@app.get("/api/monitoring/health")
async def health_check() -> Dict[str, Any]:
    """健康检查"""

@app.get("/api/monitoring/performance/stats")
async def get_performance_stats(
    operation: Optional[str] = None,
    time_window: int = 3600
) -> Dict[str, Any]:
    """获取性能统计"""
```

---

## 🔄 数据流程

### 音频处理流程

```
[音频输入] → [VAD检测] → [特征提取] → [LID识别] → [ASR识别] → [文本输出]
     ↓           ↓           ↓           ↓           ↓           ↓
  PCM 16bit → 语音段检测 → FBANK特征 → 语种检测 → CTC解码 → 后处理文本
```

### 会话处理流程

```python
async def process_session_workflow(session: Session, audio_data: bytes):
    """会话处理工作流"""
    
    # 1. VAD检测
    is_speech = session.vad_processor.is_speech_chunk(audio_data)
    if not is_speech:
        return {"type": "silence"}
    
    # 2. 语种检测（如果启用）
    if session.auto_language_detection and not session.language_detected:
        language, confidence = await session.lid_engine.detect_language(audio_data)
        if language:
            session.switch_language(language)
    
    # 3. ASR识别
    result = await session.asr_engine.process_audio_chunk(audio_data)
    
    # 4. 返回结果
    return {
        "type": "recognition_result",
        "text": result.get("text", ""),
        "is_final": result.get("is_final", False),
        "confidence": result.get("confidence", 0.0),
        "language": session.current_language
    }
```

---

## 🚀 扩展开发

### 1. 添加新语种支持

#### 步骤1: 准备模型文件

```bash
# 创建语种目录
mkdir models/new_language

# 放置必需文件
models/new_language/
├── encoder.onnx      # 编码器模型
├── ctc.onnx         # CTC模型
├── units.txt        # 词汇表
└── hotwords.txt     # 热词文件(可选)
```

#### 步骤2: 创建配置文件

```yaml
# configs/lang_configs/new_language.yaml
code: "new_lang"
name: "New Language"
separator: ",.!?;:"
silence_threshold: 0.35

model:
  model_path: "models/new_language"
  dict_path: "models/new_language/units.txt"
  hotwords_path: "models/new_language/hotwords.txt"
  chunk_size: 16
  left_chunks: 16
  decoding_window: 67
  subsampling_rate: 4
  right_context: 7

features:
  enable_punctuation: true
  enable_itn: true
  enable_hotwords: true
```

#### 步骤3: 更新LID配置

```yaml
# configs/lid_config.yaml
language_mapping:
  0: "zh"
  1: "en"
  2: "ru"
  3: "ug"
  4: "kk"
  5: "new_lang"  # 添加新语种
```

#### 步骤4: 实现语种特定处理

```python
# core/asr/text_processor.py
class TextProcessor:
    def __init__(self, language: str):
        self.language = language
        self.processors = {
            'zh': self._process_chinese,
            'en': self._process_english,
            'new_lang': self._process_new_language  # 添加处理器
        }

    def _process_new_language(self, text: str) -> str:
        """新语种文本后处理"""
        # 实现特定的文本处理逻辑
        return text
```

### 2. 自定义音频处理器

#### 创建自定义VAD

```python
# core/audio/custom_vad.py
from .base_vad import BaseVAD

class CustomVAD(BaseVAD):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        # 初始化自定义VAD

    def is_speech_chunk(self, audio_chunk: np.ndarray) -> bool:
        """自定义语音检测逻辑"""
        # 实现检测算法
        return True

    def detect_speech_segments(self, audio: np.ndarray) -> List[Tuple[float, float]]:
        """检测语音段"""
        # 实现分段算法
        return []
```

#### 注册自定义处理器

```python
# core/audio/__init__.py
from .vad_processor import VADProcessor
from .custom_vad import CustomVAD

VAD_REGISTRY = {
    'webrtcvad': VADProcessor,
    'custom': CustomVAD
}

def create_vad_processor(vad_type: str, config: Dict[str, Any]):
    """VAD处理器工厂函数"""
    if vad_type not in VAD_REGISTRY:
        raise ValueError(f"Unknown VAD type: {vad_type}")

    return VAD_REGISTRY[vad_type](config)
```

### 3. 扩展监控指标

#### 添加自定义指标

```python
# utils/monitoring/custom_metrics.py
from .performance_monitor import global_performance_monitor

class CustomMetrics:
    def __init__(self):
        self.custom_counters = {}

    def increment_counter(self, name: str, value: int = 1):
        """增加计数器"""
        self.custom_counters[name] = self.custom_counters.get(name, 0) + value

    def record_custom_metric(self, session_id: str, metric_name: str, value: float):
        """记录自定义指标"""
        global_performance_monitor.record_operation(
            session_id, f"custom_{metric_name}", value
        )

    def get_custom_stats(self) -> Dict[str, Any]:
        """获取自定义统计"""
        return {
            "counters": self.custom_counters,
            "timestamp": time.time()
        }

# 全局实例
custom_metrics = CustomMetrics()
```

#### 集成到监控API

```python
# api/monitoring/monitoring_api.py
@router.get("/custom/metrics")
async def get_custom_metrics() -> Dict[str, Any]:
    """获取自定义指标"""
    from ...utils.monitoring.custom_metrics import custom_metrics
    return custom_metrics.get_custom_stats()
```

### 4. 插件系统设计

#### 插件接口定义

```python
# core/plugins/base_plugin.py
from abc import ABC, abstractmethod
from typing import Dict, Any

class BasePlugin(ABC):
    """插件基类"""

    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> bool:
        """初始化插件"""
        pass

    @abstractmethod
    def process(self, data: Any) -> Any:
        """处理数据"""
        pass

    @abstractmethod
    def cleanup(self):
        """清理资源"""
        pass

    @property
    @abstractmethod
    def name(self) -> str:
        """插件名称"""
        pass

    @property
    @abstractmethod
    def version(self) -> str:
        """插件版本"""
        pass
```

#### 插件管理器

```python
# core/plugins/plugin_manager.py
class PluginManager:
    def __init__(self):
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_configs: Dict[str, Dict[str, Any]] = {}

    def register_plugin(self, plugin: BasePlugin, config: Dict[str, Any] = None):
        """注册插件"""
        if plugin.initialize(config or {}):
            self.plugins[plugin.name] = plugin
            self.plugin_configs[plugin.name] = config
            logger.info(f"Plugin registered: {plugin.name} v{plugin.version}")
        else:
            logger.error(f"Failed to initialize plugin: {plugin.name}")

    def get_plugin(self, name: str) -> Optional[BasePlugin]:
        """获取插件"""
        return self.plugins.get(name)

    def process_with_plugin(self, plugin_name: str, data: Any) -> Any:
        """使用插件处理数据"""
        plugin = self.get_plugin(plugin_name)
        if plugin:
            return plugin.process(data)
        return data

    def cleanup_all(self):
        """清理所有插件"""
        for plugin in self.plugins.values():
            try:
                plugin.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up plugin {plugin.name}: {e}")

# 全局插件管理器
global_plugin_manager = PluginManager()
```

---

## 🧪 测试指南

### 1. 测试架构

```
tests/
├── unit/                  # 单元测试
│   ├── test_session_manager.py
│   ├── test_asr_engine.py
│   ├── test_lid_engine.py
│   └── test_vad_processor.py
├── integration/           # 集成测试
│   ├── test_websocket_api.py
│   ├── test_http_api.py
│   └── test_end_to_end.py
├── performance/           # 性能测试
│   ├── test_load.py
│   ├── test_stress.py
│   └── test_benchmark.py
├── fixtures/              # 测试数据
│   ├── audio_samples/
│   └── config_samples/
└── conftest.py           # pytest配置
```

### 2. 单元测试示例

```python
# tests/unit/test_session_manager.py
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from core.session import SessionManager
from api.websocket.protocol import HandshakeRequest

class TestSessionManager:

    @pytest.fixture
    async def session_manager(self):
        """创建测试用的会话管理器"""
        config_manager = Mock()
        config_manager.get_supported_languages.return_value = ["zh", "en"]

        manager = SessionManager(config_manager)
        yield manager
        await manager.shutdown()

    @pytest.mark.asyncio
    async def test_create_session_success(self, session_manager):
        """测试成功创建会话"""
        handshake_req = HandshakeRequest(
            client_id="test_client",
            language="zh",
            auto_language_detection=False
        )

        websocket = Mock()
        success = await session_manager.create_session(
            "test_session", "test_client", handshake_req, websocket
        )

        assert success
        assert "test_session" in session_manager.sessions

    @pytest.mark.asyncio
    async def test_process_audio_data(self, session_manager):
        """测试音频数据处理"""
        # 先创建会话
        await self._create_test_session(session_manager)

        # 模拟音频数据
        audio_message = Mock()
        audio_message.audio_data = [0] * 1600  # 0.1秒的音频
        audio_message.is_last = False

        result = await session_manager.process_audio_data("test_session", audio_message)

        assert result["success"] is True
        assert "text" in result

    async def _create_test_session(self, session_manager):
        """创建测试会话的辅助方法"""
        handshake_req = HandshakeRequest(
            client_id="test_client",
            language="zh",
            auto_language_detection=False
        )
        websocket = Mock()
        await session_manager.create_session("test_session", "test_client", handshake_req, websocket)
```

### 3. 集成测试示例

```python
# tests/integration/test_websocket_api.py
import pytest
import json
from fastapi.testclient import TestClient
from fastapi.websockets import WebSocket

from server import app

class TestWebSocketAPI:

    @pytest.fixture
    def client(self):
        return TestClient(app)

    def test_websocket_handshake(self, client):
        """测试WebSocket握手"""
        with client.websocket_connect("/ws/stream") as websocket:
            # 发送握手消息
            handshake = {
                "type": "handshake",
                "client_id": "test_client",
                "language": "zh",
                "auto_language_detection": False,
                "audio_config": {
                    "sample_rate": 16000,
                    "channels": 1,
                    "chunk_duration": 0.4
                }
            }
            websocket.send_json(handshake)

            # 接收响应
            response = websocket.receive_json()

            assert response["type"] == "handshake_response"
            assert response["success"] is True

    def test_audio_processing_workflow(self, client):
        """测试完整的音频处理流程"""
        with client.websocket_connect("/ws/stream") as websocket:
            # 握手
            self._perform_handshake(websocket)

            # 发送音频数据
            audio_data = [0] * 6400  # 0.4秒的静音
            audio_message = {
                "type": "audio_data",
                "sequence_id": 1,
                "audio_data": audio_data,
                "is_last": False,
                "timestamp": 1640995200000
            }
            websocket.send_json(audio_message)

            # 接收处理结果
            response = websocket.receive_json()

            assert response["type"] in ["recognition_result", "silence"]

    def _perform_handshake(self, websocket):
        """执行握手的辅助方法"""
        handshake = {
            "type": "handshake",
            "client_id": "test_client",
            "language": "zh",
            "auto_language_detection": False,
            "audio_config": {
                "sample_rate": 16000,
                "channels": 1,
                "chunk_duration": 0.4
            }
        }
        websocket.send_json(handshake)
        response = websocket.receive_json()
        assert response["success"] is True
```

### 4. 性能测试

```python
# tests/performance/test_load.py
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

class LoadTest:

    def __init__(self, base_url: str = "ws://localhost:8080"):
        self.base_url = base_url
        self.results = []

    async def single_session_test(self, session_id: str) -> Dict[str, float]:
        """单个会话的性能测试"""
        start_time = time.time()

        try:
            # 建立连接和握手
            connect_start = time.time()
            # ... WebSocket连接逻辑
            connect_time = time.time() - connect_start

            # 发送音频数据
            process_start = time.time()
            # ... 音频处理逻辑
            process_time = time.time() - process_start

            total_time = time.time() - start_time

            return {
                "session_id": session_id,
                "connect_time": connect_time,
                "process_time": process_time,
                "total_time": total_time,
                "success": True
            }

        except Exception as e:
            return {
                "session_id": session_id,
                "error": str(e),
                "success": False
            }

    async def run_load_test(self, concurrent_sessions: int = 10, duration: int = 60):
        """运行负载测试"""
        print(f"Starting load test: {concurrent_sessions} concurrent sessions for {duration}s")

        tasks = []
        for i in range(concurrent_sessions):
            task = asyncio.create_task(
                self.single_session_test(f"session_{i}")
            )
            tasks.append(task)

        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 分析结果
        self.analyze_results(results)

    def analyze_results(self, results: List[Dict[str, Any]]):
        """分析测试结果"""
        successful_results = [r for r in results if r.get("success", False)]
        failed_count = len(results) - len(successful_results)

        if successful_results:
            connect_times = [r["connect_time"] for r in successful_results]
            process_times = [r["process_time"] for r in successful_results]
            total_times = [r["total_time"] for r in successful_results]

            print(f"Success Rate: {len(successful_results)}/{len(results)} ({len(successful_results)/len(results)*100:.1f}%)")
            print(f"Failed Sessions: {failed_count}")
            print(f"Average Connect Time: {statistics.mean(connect_times):.3f}s")
            print(f"Average Process Time: {statistics.mean(process_times):.3f}s")
            print(f"Average Total Time: {statistics.mean(total_times):.3f}s")
            print(f"P95 Total Time: {statistics.quantiles(total_times, n=20)[18]:.3f}s")
        else:
            print("All sessions failed!")

if __name__ == "__main__":
    load_test = LoadTest()
    asyncio.run(load_test.run_load_test(concurrent_sessions=50, duration=120))
```

### 5. 测试配置

```python
# conftest.py
import pytest
import asyncio
from pathlib import Path

# 测试配置
TEST_CONFIG = {
    "server": {
        "host": "127.0.0.1",
        "port": 8081,  # 使用不同端口避免冲突
        "log_level": "DEBUG"
    },
    "audio": {
        "sample_rate": 16000,
        "channels": 1,
        "chunk_duration": 0.4
    }
}

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def test_audio_data():
    """测试音频数据"""
    # 生成0.4秒的测试音频（16kHz, 16bit, mono）
    sample_rate = 16000
    duration = 0.4
    samples = int(sample_rate * duration)

    # 生成正弦波测试信号
    import numpy as np
    t = np.linspace(0, duration, samples)
    frequency = 440  # A4音符
    audio = np.sin(2 * np.pi * frequency * t) * 0.5

    # 转换为int16
    audio_int16 = (audio * 32767).astype(np.int16)
    return audio_int16.tolist()

@pytest.fixture
def test_config_dir(tmp_path):
    """创建测试配置目录"""
    config_dir = tmp_path / "test_configs"
    config_dir.mkdir()

    # 创建测试配置文件
    import yaml

    server_config = config_dir / "server_config.yaml"
    with open(server_config, 'w') as f:
        yaml.dump(TEST_CONFIG, f)

    return config_dir
```

---

## 🚀 部署指南

### 1. 开发环境部署

```bash
# 启动开发服务器
python server.py --debug --reload

# 使用uvicorn直接启动
uvicorn server:app --host 0.0.0.0 --port 8080 --reload
```

### 2. 生产环境部署

#### 使用Gunicorn + Uvicorn

```bash
# 安装生产依赖
pip install gunicorn

# 启动生产服务器
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8080 \
  --timeout 300 \
  --keepalive 2 \
  --max-requests 1000 \
  --max-requests-jitter 100 \
  --preload \
  server:app
```

#### Systemd服务配置

```ini
# /etc/systemd/system/enhanced-stream-asr.service
[Unit]
Description=Enhanced Stream ASR Service
After=network.target

[Service]
Type=exec
User=asr
Group=asr
WorkingDirectory=/opt/enhanced-stream-asr
Environment=PATH=/opt/enhanced-stream-asr/venv/bin
ExecStart=/opt/enhanced-stream-asr/venv/bin/gunicorn \
  -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8080 \
  --timeout 300 \
  server:app
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

#### Docker部署

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    libffi-dev \
    libssl-dev \
    libasound2-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 asr && chown -R asr:asr /app
USER asr

# 暴露端口
EXPOSE 8080

# 启动命令
CMD ["gunicorn", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8080", "server:app"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  asr-server:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./models:/app/models:ro
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
    environment:
      - LOG_LEVEL=INFO
      - WORKERS=4
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/monitoring/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - asr-server
    restart: unless-stopped
```

### 3. Kubernetes部署

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: enhanced-stream-asr
  labels:
    app: enhanced-stream-asr
spec:
  replicas: 3
  selector:
    matchLabels:
      app: enhanced-stream-asr
  template:
    metadata:
      labels:
        app: enhanced-stream-asr
    spec:
      containers:
      - name: asr-server
        image: enhanced-stream-asr:latest
        ports:
        - containerPort: 8080
        env:
        - name: WORKERS
          value: "2"
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: models
          mountPath: /app/models
          readOnly: true
        - name: configs
          mountPath: /app/configs
          readOnly: true
        livenessProbe:
          httpGet:
            path: /api/monitoring/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/monitoring/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: models
        persistentVolumeClaim:
          claimName: asr-models-pvc
      - name: configs
        configMap:
          name: asr-configs

---
apiVersion: v1
kind: Service
metadata:
  name: enhanced-stream-asr-service
spec:
  selector:
    app: enhanced-stream-asr
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```
